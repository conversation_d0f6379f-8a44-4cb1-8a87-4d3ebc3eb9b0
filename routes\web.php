<?php

use App\Http\Controllers\WebController;
use Illuminate\Support\Facades\Route;

// Main route - Enhanced UI (New Default)
Route::get('/', function () {
    $personalities = app(\App\Services\PersonalityModifierService::class)->getAvailablePersonalities();
    return view('chat-enhanced-ui', compact('personalities'));
});

// Legacy UI routes for backward compatibility
Route::get('/legacy-ui', [WebController::class, 'index']);

// Vite UI route removed - no longer needed

Route::get('/production-ui', function () {
    $personalities = app(\App\Services\PersonalityModifierService::class)->getAvailablePersonalities();
    return view('chat-production', compact('personalities'));
});

// Enhanced UI route (same as main route)
Route::get('/enhanced-ui', function () {
    $personalities = app(\App\Services\PersonalityModifierService::class)->getAvailablePersonalities();
    return view('chat-enhanced-ui', compact('personalities'));
});
