<?php

use App\Http\Controllers\WebController;
use Illuminate\Support\Facades\Route;

// Main route - Modern WIDDX Interface
Route::get('/', function () {
    return view('widdx-modern');
});

// Alternative interfaces for testing
Route::get('/old', function () {
    return view('grok-interface');
});

Route::get('/legacy', [WebController::class, 'index']);

// API routes are defined in routes/api.php
