/**
 * WIDDX AI Enhanced UI JavaScript
 * Handles the new unified chat interface with improved UX
 */

class WiddxEnhancedUI {
    constructor() {
        this.isTyping = false;
        this.messageHistory = [];
        this.currentTheme = localStorage.getItem('widdx-theme') || 'dark';
        this.deepThinkMode = false;

        this.init();
    }

    init() {
        this.setupEventListeners();
        this.setupTheme();
        this.setupAutoResize();
        this.setupAccessibility();
        this.loadChatHistory();

        console.log('✅ WIDDX Enhanced UI initialized');
    }

    setupEventListeners() {
        // Chat form submission
        const chatForm = document.getElementById('chat-form');
        if (chatForm) {
            chatForm.addEventListener('submit', (e) => this.handleSubmit(e));
        }

        // Deep think toggle
        const deepThinkToggle = document.getElementById('deep-think-toggle');
        if (deepThinkToggle) {
            deepThinkToggle.addEventListener('click', () => this.toggleDeepThink());
        }

        // Theme toggle
        const themeToggle = document.getElementById('theme-toggle');
        if (themeToggle) {
            themeToggle.addEventListener('click', () => this.toggleTheme());
        }

        // Settings toggle
        const settingsToggle = document.getElementById('settings-toggle');
        if (settingsToggle) {
            settingsToggle.addEventListener('click', () => this.toggleSettings());
        }

        // Input field enhancements
        const messageInput = document.getElementById('message-input');
        if (messageInput) {
            messageInput.addEventListener('input', () => this.handleInputChange());
            messageInput.addEventListener('keydown', (e) => this.handleKeyDown(e));
            messageInput.addEventListener('paste', (e) => this.handlePaste(e));
        }
    }

    setupTheme() {
        const html = document.documentElement;
        const themeToggle = document.getElementById('theme-toggle');
        const themeIcon = themeToggle?.querySelector('i');

        if (this.currentTheme === 'light') {
            html.setAttribute('data-theme', 'light');
            if (themeIcon) {
                themeIcon.className = 'fas fa-sun';
            }
        } else {
            html.setAttribute('data-theme', 'dark');
            if (themeIcon) {
                themeIcon.className = 'fas fa-moon';
            }
        }
    }

    setupAutoResize() {
        const textarea = document.getElementById('message-input');
        if (!textarea) return;

        textarea.addEventListener('input', function() {
            // Reset height to auto to get the correct scrollHeight
            this.style.height = 'auto';

            // Set the height to scrollHeight with min/max constraints
            const newHeight = Math.min(Math.max(this.scrollHeight, 52), 200);
            this.style.height = newHeight + 'px';

            // Update send button state
            const sendButton = document.getElementById('send-button');
            if (sendButton) {
                sendButton.disabled = !this.value.trim();
            }
        });
    }

    setupAccessibility() {
        // Add ARIA live region for announcements
        if (!document.getElementById('aria-announcements')) {
            const announcements = document.createElement('div');
            announcements.id = 'aria-announcements';
            announcements.setAttribute('aria-live', 'polite');
            announcements.setAttribute('aria-atomic', 'true');
            announcements.className = 'widdx-sr-only';
            document.body.appendChild(announcements);
        }

        // Setup keyboard navigation
        this.setupKeyboardNavigation();
    }

    setupKeyboardNavigation() {
        document.addEventListener('keydown', (e) => {
            // Global keyboard shortcuts
            if (e.ctrlKey || e.metaKey) {
                switch(e.key) {
                    case 'Enter':
                        e.preventDefault();
                        this.submitMessage();
                        break;
                    case '/':
                        e.preventDefault();
                        document.getElementById('message-input')?.focus();
                        break;
                    case 'd':
                        e.preventDefault();
                        this.toggleDeepThink();
                        break;
                }
            }

            // Escape key actions
            if (e.key === 'Escape') {
                this.handleEscape();
            }
        });
    }

    handleSubmit(e) {
        e.preventDefault();
        this.submitMessage();
    }

    async submitMessage() {
        const messageInput = document.getElementById('message-input');
        const message = messageInput?.value.trim();

        if (!message || this.isTyping) return;

        // Add user message to chat
        this.addMessage(message, 'user');

        // Clear input and reset height
        messageInput.value = '';
        messageInput.style.height = 'auto';
        document.getElementById('send-button').disabled = true;

        // Hide welcome section
        this.hideWelcome();

        // Show typing indicator
        this.showTyping();

        try {
            // Send message to backend
            const response = await this.sendToBackend(message);

            // Hide typing indicator
            this.hideTyping();

            // Add assistant response
            this.addMessage(response.message, 'assistant');

            // Handle special responses (images, etc.)
            if (response.type === 'image') {
                this.handleImageResponse(response);
            }

        } catch (error) {
            console.error('Error sending message:', error);
            this.hideTyping();
            this.addMessage('عذراً، حدث خطأ في الاتصال. يرجى المحاولة مرة أخرى.', 'assistant', 'error');
        }
    }

    async sendToBackend(message) {
        const response = await fetch('/api/chat', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.content
            },
            body: JSON.stringify({
                message: message,
                deep_think: this.deepThinkMode,
                conversation_id: this.getConversationId()
            })
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        return await response.json();
    }

    addMessage(content, sender, type = 'normal') {
        const messagesContainer = document.getElementById('messages-list');
        if (!messagesContainer) return;

        const messageElement = this.createMessageElement(content, sender, type);
        messagesContainer.appendChild(messageElement);

        // Scroll to bottom
        this.scrollToBottom();

        // Announce to screen readers
        this.announceMessage(content, sender);

        // Save to history
        this.messageHistory.push({ content, sender, type, timestamp: Date.now() });
        this.saveChatHistory();
    }

    createMessageElement(content, sender, type = 'normal') {
        const messageDiv = document.createElement('div');
        messageDiv.className = `widdx-message ${sender}`;

        if (type === 'error') {
            messageDiv.classList.add('error');
        }

        const wrapper = document.createElement('div');
        wrapper.className = 'widdx-message-wrapper';

        const avatar = document.createElement('div');
        avatar.className = 'widdx-message-avatar';
        avatar.textContent = sender === 'user' ? 'أ' : 'W';
        avatar.setAttribute('aria-hidden', 'true');

        const messageContent = document.createElement('div');
        messageContent.className = 'widdx-message-content';

        const bubble = document.createElement('div');
        bubble.className = 'widdx-message-bubble';
        bubble.innerHTML = this.formatMessage(content);

        messageContent.appendChild(bubble);
        wrapper.appendChild(avatar);
        wrapper.appendChild(messageContent);
        messageDiv.appendChild(wrapper);

        // Add animation
        requestAnimationFrame(() => {
            messageDiv.style.opacity = '1';
        });

        return messageDiv;
    }

    formatMessage(content) {
        // Basic markdown-like formatting
        return content
            .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
            .replace(/\*(.*?)\*/g, '<em>$1</em>')
            .replace(/`(.*?)`/g, '<code>$1</code>')
            .replace(/\n/g, '<br>');
    }

    showTyping() {
        this.isTyping = true;
        const typingIndicator = document.getElementById('typing-indicator');
        if (typingIndicator) {
            typingIndicator.classList.remove('hidden');
            this.scrollToBottom();
        }

        // Update status
        this.updateStatus('يكتب...');
    }

    hideTyping() {
        this.isTyping = false;
        const typingIndicator = document.getElementById('typing-indicator');
        if (typingIndicator) {
            typingIndicator.classList.add('hidden');
        }

        // Update status
        this.updateStatus('جاهز للمساعدة');
    }

    hideWelcome() {
        const welcomeSection = document.getElementById('welcome-section');
        if (welcomeSection) {
            welcomeSection.style.display = 'none';
        }
    }

    scrollToBottom() {
        const messagesArea = document.querySelector('.widdx-messages-area');
        if (messagesArea) {
            messagesArea.scrollTop = messagesArea.scrollHeight;
        }
    }

    toggleDeepThink() {
        this.deepThinkMode = !this.deepThinkMode;
        const toggle = document.getElementById('deep-think-toggle');

        if (toggle) {
            toggle.classList.toggle('active', this.deepThinkMode);
            toggle.setAttribute('aria-pressed', this.deepThinkMode.toString());
        }

        this.announceToScreenReader(
            this.deepThinkMode ? 'تم تفعيل وضع التفكير العميق' : 'تم إلغاء وضع التفكير العميق'
        );
    }

    toggleTheme() {
        this.currentTheme = this.currentTheme === 'dark' ? 'light' : 'dark';
        localStorage.setItem('widdx-theme', this.currentTheme);
        this.setupTheme();

        this.announceToScreenReader(
            this.currentTheme === 'dark' ? 'تم التبديل إلى المظهر الداكن' : 'تم التبديل إلى المظهر الفاتح'
        );
    }

    toggleSettings() {
        const settingsPanel = document.getElementById('settings-panel');
        if (settingsPanel) {
            settingsPanel.classList.toggle('hidden');
        }
    }

    updateStatus(status) {
        const statusIndicator = document.getElementById('status-indicator');
        if (statusIndicator) {
            statusIndicator.textContent = status;
        }
    }

    announceMessage(content, sender) {
        const announcement = sender === 'user' ? 'رسالة مرسلة' : 'رد جديد من WIDDX AI';
        this.announceToScreenReader(`${announcement}: ${content.substring(0, 100)}${content.length > 100 ? '...' : ''}`);
    }

    announceToScreenReader(message) {
        const announcements = document.getElementById('aria-announcements');
        if (announcements) {
            announcements.textContent = message;
        }
    }

    handleInputChange() {
        const messageInput = document.getElementById('message-input');
        const sendButton = document.getElementById('send-button');

        if (messageInput && sendButton) {
            sendButton.disabled = !messageInput.value.trim();
        }
    }

    handleKeyDown(e) {
        if ((e.ctrlKey || e.metaKey) && e.key === 'Enter') {
            e.preventDefault();
            this.submitMessage();
        }
    }

    handlePaste(e) {
        // Handle image paste in the future
        console.log('Paste event detected');
    }

    handleEscape() {
        const messageInput = document.getElementById('message-input');
        if (messageInput && document.activeElement === messageInput) {
            messageInput.value = '';
            messageInput.style.height = 'auto';
            document.getElementById('send-button').disabled = true;
        }
    }

    getConversationId() {
        // Generate or retrieve conversation ID
        let conversationId = sessionStorage.getItem('widdx-conversation-id');
        if (!conversationId) {
            conversationId = 'conv_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
            sessionStorage.setItem('widdx-conversation-id', conversationId);
        }
        return conversationId;
    }

    saveChatHistory() {
        try {
            localStorage.setItem('widdx-chat-history', JSON.stringify(this.messageHistory.slice(-50))); // Keep last 50 messages
        } catch (error) {
            console.warn('Could not save chat history:', error);
        }
    }

    loadChatHistory() {
        try {
            const history = localStorage.getItem('widdx-chat-history');
            if (history) {
                this.messageHistory = JSON.parse(history);
                // Optionally restore recent messages
                // this.restoreRecentMessages();
            }
        } catch (error) {
            console.warn('Could not load chat history:', error);
        }
    }

    handleImageResponse(response) {
        // Handle image generation response
        if (response.images && response.images.length > 0) {
            this.addImageMessage(response.images, response.prompt || 'صورة مولدة');
        }
    }

    addImageMessage(images, prompt) {
        const messagesContainer = document.getElementById('messages-list');
        if (!messagesContainer) return;

        const messageElement = this.createImageMessageElement(images, prompt);
        messagesContainer.appendChild(messageElement);

        // Scroll to bottom
        this.scrollToBottom();

        // Save to history
        this.messageHistory.push({
            content: `[صورة: ${prompt}]`,
            sender: 'assistant',
            type: 'image',
            images: images,
            timestamp: Date.now()
        });
        this.saveChatHistory();
    }

    createImageMessageElement(images, prompt) {
        const messageDiv = document.createElement('div');
        messageDiv.className = 'widdx-message assistant image';

        const wrapper = document.createElement('div');
        wrapper.className = 'widdx-message-wrapper';

        const avatar = document.createElement('div');
        avatar.className = 'widdx-message-avatar';
        avatar.textContent = 'W';
        avatar.setAttribute('aria-hidden', 'true');

        const messageContent = document.createElement('div');
        messageContent.className = 'widdx-message-content';

        const bubble = document.createElement('div');
        bubble.className = 'widdx-message-bubble';

        // Add prompt text
        const promptText = document.createElement('p');
        promptText.textContent = `تم إنشاء الصورة: ${prompt}`;
        promptText.style.marginBottom = 'var(--space-3)';
        bubble.appendChild(promptText);

        // Create image container
        const imageContainer = document.createElement('div');
        imageContainer.className = 'widdx-image-container';

        if (images.length === 1) {
            imageContainer.classList.add('single');
        } else if (images.length === 2) {
            imageContainer.classList.add('double');
        } else if (images.length <= 4) {
            imageContainer.classList.add('quad');
        }

        // Create image gallery
        const gallery = document.createElement('div');
        gallery.className = `widdx-image-gallery ${images.length === 1 ? 'single' : images.length === 2 ? 'double' : 'quad'}`;

        images.forEach((imageUrl, index) => {
            const img = document.createElement('img');
            img.src = imageUrl;
            img.alt = `${prompt} - صورة ${index + 1}`;
            img.className = 'widdx-generated-image';
            img.loading = 'lazy';

            // Add click handler for modal
            img.addEventListener('click', () => this.openImageModal(imageUrl, prompt));

            gallery.appendChild(img);
        });

        imageContainer.appendChild(gallery);

        // Add metadata
        const metadata = document.createElement('div');
        metadata.className = 'widdx-image-metadata';

        const promptDisplay = document.createElement('div');
        promptDisplay.className = 'widdx-image-prompt';
        promptDisplay.textContent = `الوصف: "${prompt}"`;
        metadata.appendChild(promptDisplay);

        // Add action buttons
        const actions = document.createElement('div');
        actions.className = 'widdx-image-actions';

        images.forEach((imageUrl, index) => {
            const downloadBtn = document.createElement('a');
            downloadBtn.href = imageUrl;
            downloadBtn.download = `widdx-image-${Date.now()}-${index + 1}.png`;
            downloadBtn.className = 'widdx-image-action-btn';
            downloadBtn.innerHTML = '<i class="fas fa-download" aria-hidden="true"></i> تحميل';
            actions.appendChild(downloadBtn);
        });

        metadata.appendChild(actions);
        imageContainer.appendChild(metadata);

        bubble.appendChild(imageContainer);
        messageContent.appendChild(bubble);
        wrapper.appendChild(avatar);
        wrapper.appendChild(messageContent);
        messageDiv.appendChild(wrapper);

        // Add animation
        requestAnimationFrame(() => {
            messageDiv.style.opacity = '1';
        });

        return messageDiv;
    }

    openImageModal(imageUrl, prompt) {
        // Create modal if it doesn't exist
        let modal = document.getElementById('widdx-image-modal');
        if (!modal) {
            modal = this.createImageModal();
            document.body.appendChild(modal);
        }

        // Update modal content
        const modalImage = modal.querySelector('.widdx-image-modal-image');
        modalImage.src = imageUrl;
        modalImage.alt = prompt;

        // Show modal
        modal.classList.add('active');

        // Focus management
        modal.focus();

        // Announce to screen readers
        this.announceToScreenReader(`فتح الصورة في نافذة منبثقة: ${prompt}`);
    }

    createImageModal() {
        const modal = document.createElement('div');
        modal.id = 'widdx-image-modal';
        modal.className = 'widdx-image-modal';
        modal.setAttribute('role', 'dialog');
        modal.setAttribute('aria-modal', 'true');
        modal.setAttribute('aria-label', 'عرض الصورة');
        modal.tabIndex = -1;

        const content = document.createElement('div');
        content.className = 'widdx-image-modal-content';

        const image = document.createElement('img');
        image.className = 'widdx-image-modal-image';

        const closeBtn = document.createElement('button');
        closeBtn.className = 'widdx-image-modal-close';
        closeBtn.innerHTML = '<i class="fas fa-times" aria-hidden="true"></i>';
        closeBtn.setAttribute('aria-label', 'إغلاق النافذة المنبثقة');
        closeBtn.addEventListener('click', () => this.closeImageModal());

        content.appendChild(image);
        content.appendChild(closeBtn);
        modal.appendChild(content);

        // Close on background click
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                this.closeImageModal();
            }
        });

        // Close on Escape key
        modal.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                this.closeImageModal();
            }
        });

        return modal;
    }

    closeImageModal() {
        const modal = document.getElementById('widdx-image-modal');
        if (modal) {
            modal.classList.remove('active');

            // Return focus to the image that opened the modal
            const lastFocusedImage = document.querySelector('.widdx-generated-image:focus');
            if (lastFocusedImage) {
                lastFocusedImage.focus();
            }

            this.announceToScreenReader('تم إغلاق النافذة المنبثقة');
        }
    }
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    window.widdxEnhancedUI = new WiddxEnhancedUI();
});
